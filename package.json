{"name": "chwadvocates-cms", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "devDependencies": {}, "dependencies": {"firebase-admin": "^9.11.0", "knex": "0.21.18", "pg": "^8.7.1", "pg-connection-string": "^2.5.0", "react-quill": "^1.3.5", "slugify": "^1.6.0", "sqlite3": "5.0.0", "strapi": "3.6.5", "strapi-admin": "3.6.5", "strapi-connector-bookshelf": "3.6.5", "strapi-plugin-content-manager": "3.6.5", "strapi-plugin-content-type-builder": "3.6.5", "strapi-plugin-email": "3.6.5", "strapi-plugin-i18n": "3.6.5", "strapi-plugin-import-export-content": "^0.4.1", "strapi-plugin-upload": "3.6.5", "strapi-plugin-users-permissions": "3.6.5", "strapi-provider-email-sendgrid": "^3.6.11", "strapi-utils": "3.6.5"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "7c7a8f5c-c4ea-40d2-b33b-750b062f181e"}, "engines": {"node": ">=10.16.0 <=14.x.x", "npm": "^6.0.0"}, "license": "MIT"}