// Local - SQLITE test DB

// module.exports = ({ env }) => ({
//   defaultConnection: "default",
//   connections: {
//     default: {
//       connector: "bookshelf",
//       settings: {
//         client: "sqlite",
//         filename: env("DATABASE_FILENAME", ".tmp/data.db"),
//       },
//       options: {
//         useNullAsDefault: true,
//       },
//     },
//   },
// });

// Local -  POSTGRES staging DB

// module.exports = ({ env }) => ({
//   defaultConnection: "default",
//   connections: {
//     default: {
//       connector: "bookshelf",
//       settings: {
//         client: "postgres",
//         host: env("DATABASE_HOST", "localhost"),
//         port: env.int("DATABASE_PORT", 5432),
//         database: env("DATABASE_NAME", "chw_live_content"),
//         username: env("DATABASE_USERNAME", "chw_live"),
//         password: env("DATABASE_PASSWORD", "123456"),
//         schema: env("DATABASE_SCHEMA", "public"),
//       },
//       options: {},
//     },
//   },
// });

// Live DB

const parse = require("pg-connection-string").parse;
module.exports = ({ env }) => ({
  defaultConnection: "default",
  connections: {
    default: {
      connector: "bookshelf",
      settings: {
        client: "postgres",
        host: env(
          "DATABASE_HOST",
          "app-2b698fc6-2950-4757-92ca-117d82e9c098-do-user-9695707-0.b.db.ondigitalocean.com"
        ),
        port: env.int("DATABASE_PORT", 25060),
        database: env("DATABASE_NAME", "db"),
        username: env("DATABASE_USERNAME", "db"),
        password: env("DATABASE_PASSWORD", "k832v6s52cno33qi"),
        schema: env("DATABASE_SCHEMA", "public"),
        ssl: {
          rejectUnauthorized: false,
        },
      },
      options: {},
    },
  },
});
