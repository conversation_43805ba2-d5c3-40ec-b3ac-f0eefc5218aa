{"kind": "collectionType", "collectionName": "units", "info": {"name": "Unit", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "outline": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"model": "file", "via": "related", "allowedTypes": ["images", "files", "videos"], "plugin": "upload", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "unit_info": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "activity_list": {"model": "activity-list"}, "index": {"pluginOptions": {"i18n": {"localized": true}}, "type": "integer"}}}