{"routes": [{"method": "GET", "path": "/activity-lists", "handler": "activity-list.find", "config": {"policies": []}}, {"method": "GET", "path": "/activity-lists/count", "handler": "activity-list.count", "config": {"policies": []}}, {"method": "GET", "path": "/activity-lists/:id", "handler": "activity-list.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/activity-lists", "handler": "activity-list.create", "config": {"policies": []}}, {"method": "PUT", "path": "/activity-lists/:id", "handler": "activity-list.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/activity-lists/:id", "handler": "activity-list.delete", "config": {"policies": []}}]}