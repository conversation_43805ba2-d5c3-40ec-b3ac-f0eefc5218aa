{"kind": "collectionType", "collectionName": "question_sets", "info": {"name": "Question set", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"question": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "answer": {"type": "component", "repeatable": true, "component": "assessments.question-set", "pluginOptions": {"i18n": {"localized": true}}}, "multiple_answers": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}}, "single_answer": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}, "freeform": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean"}}}