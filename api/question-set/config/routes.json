{"routes": [{"method": "GET", "path": "/question-sets", "handler": "question-set.find", "config": {"policies": []}}, {"method": "GET", "path": "/question-sets/count", "handler": "question-set.count", "config": {"policies": []}}, {"method": "GET", "path": "/question-sets/:id", "handler": "question-set.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/question-sets", "handler": "question-set.create", "config": {"policies": []}}, {"method": "PUT", "path": "/question-sets/:id", "handler": "question-set.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/question-sets/:id", "handler": "question-set.delete", "config": {"policies": []}}]}