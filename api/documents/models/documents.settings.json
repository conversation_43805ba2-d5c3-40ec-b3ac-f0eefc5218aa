{"kind": "collectionType", "collectionName": "documents", "info": {"name": "documents", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"unit_id": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "content": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}}}