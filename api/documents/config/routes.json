{"routes": [{"method": "GET", "path": "/documents", "handler": "documents.find", "config": {"policies": []}}, {"method": "GET", "path": "/documents/count", "handler": "documents.count", "config": {"policies": []}}, {"method": "GET", "path": "/documents/:id", "handler": "documents.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/documents", "handler": "documents.create", "config": {"policies": []}}, {"method": "PUT", "path": "/documents/:id", "handler": "documents.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/documents/:id", "handler": "documents.delete", "config": {"policies": []}}]}