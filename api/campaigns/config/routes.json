{"routes": [{"method": "GET", "path": "/campaigns", "handler": "campaigns.find", "config": {"policies": []}}, {"method": "GET", "path": "/campaigns/count", "handler": "campaigns.count", "config": {"policies": []}}, {"method": "GET", "path": "/campaigns/:id", "handler": "campaigns.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/campaigns", "handler": "campaigns.create", "config": {"policies": []}}, {"method": "PUT", "path": "/campaigns/:id", "handler": "campaigns.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/campaigns/:id", "handler": "campaigns.delete", "config": {"policies": []}}]}