{"routes": [{"method": "GET", "path": "/progress-records", "handler": "progress-record.find", "config": {"policies": []}}, {"method": "GET", "path": "/progress-records/count", "handler": "progress-record.count", "config": {"policies": []}}, {"method": "GET", "path": "/progress-records/:id", "handler": "progress-record.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/progress-records", "handler": "progress-record.create", "config": {"policies": []}}, {"method": "PUT", "path": "/progress-records/:id", "handler": "progress-record.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/progress-records/:id", "handler": "progress-record.delete", "config": {"policies": []}}]}