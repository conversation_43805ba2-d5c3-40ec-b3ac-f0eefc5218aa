{"kind": "collectionType", "collectionName": "progress_records", "info": {"name": "Progress Record", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {}, "attributes": {"user_id": {"type": "integer"}, "unit_id": {"type": "integer"}, "activity_id": {"type": "integer"}, "completed": {"type": "boolean"}, "video_watched": {"type": "boolean"}, "last_activity": {"type": "boolean"}, "document_read": {"type": "boolean"}, "assessment_data": {"type": "json"}, "pre_score": {"type": "integer"}, "post_score": {"type": "integer"}, "current_score": {"type": "integer"}}}