{"kind": "collectionType", "collectionName": "videos", "info": {"name": "videos", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "video_poster": {"collection": "file", "via": "related", "allowedTypes": ["images", "files", "videos"], "plugin": "upload", "required": false, "pluginOptions": {"i18n": {"localized": true}}}, "video_id": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}}}