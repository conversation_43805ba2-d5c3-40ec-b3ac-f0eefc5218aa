{"routes": [{"method": "GET", "path": "/videos", "handler": "videos.find", "config": {"policies": []}}, {"method": "GET", "path": "/videos/count", "handler": "videos.count", "config": {"policies": []}}, {"method": "GET", "path": "/videos/:id", "handler": "videos.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/videos", "handler": "videos.create", "config": {"policies": []}}, {"method": "PUT", "path": "/videos/:id", "handler": "videos.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/videos/:id", "handler": "videos.delete", "config": {"policies": []}}]}