"use strict";

const { sanitizeEntity } = require("strapi-utils");

const sanitizeUser = (user) =>
  sanitizeEntity(user, {
    model: strapi.query("user", "users-permissions").model,
  });

module.exports = {
  async auth(ctx) {
    try {
      const idToken = ctx.request.body.token;

      console.log("idToken: " + idToken);

      const decodedToken = await strapi.firebase.auth().verifyIdToken(idToken);
      console.log("decodedToken: ");
      console.log(decodedToken);

      console.log(decodedToken.phone_number);

      if (decodedToken.phone_number) {
        let jwt;
        let user = await strapi.plugins[
          "users-permissions"
        ].services.user.fetch({
          phone: decodedToken.phone_number,
        });

        // if (!user) {
        //   console.log("no user");
        // }

        if (user) {
          console.log(user);
          user = sanitizeUser(user);

          jwt = strapi.plugins["users-permissions"].services.jwt.issue({
            id: user.id,
          });

          ctx.body = {
            user,
            jwt,
          };
        } else {
          console.log("no user");

          const pluginStore = await strapi.store({
            environment: "",
            type: "plugin",
            name: "users-permissions",
          });

          const settings = await pluginStore.get({
            key: "advanced",
          });

          const role = await strapi
            .query("role", "users-permissions")
            .findOne({ type: settings.default_role }, []);

          const params = {};
          params.role = role.id;
          params.phone = decodedToken.phone_number;
          params.email = decodedToken.phone_number.toString() + "@test.com";
          params.username = decodedToken.phone_number;
          params.confirmed = true;
          params.profile = false;
          params.approved = false;
          params.assessmentBadge = false;
          params.faqBadge = false;

          console.log(params);

          let user = await strapi
            .query("user", "users-permissions")
            .create(params);
          if (user) {
            user = sanitizeUser(user);
            jwt = strapi.plugins["users-permissions"].services.jwt.issue({
              id: user.id,
            });

            ctx.body = {
              user,
              jwt,
            };
          } else {
            // throw "user empty";
            console.log("no user created");
          }
        }
      } else {
        throw "email missing";
      }
    } catch (error) {
      console.log("error");
      console.log(error);

      return ctx.badRequest(null, [{ messages: [{ id: "unauthorized" }] }]);
    }
  },
};
