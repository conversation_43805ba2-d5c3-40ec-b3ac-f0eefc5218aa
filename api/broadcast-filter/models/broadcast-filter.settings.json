{"kind": "collectionType", "collectionName": "broadcast_filters", "info": {"name": "Broadcast filter", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"profession": {"type": "enumeration", "enum": ["chw", "physician", "registered_nurse", "medical_student", "other"]}, "gender": {"type": "enumeration", "enum": ["male", "female", "other"]}, "name": {"type": "string"}, "course_progress": {"type": "enumeration", "enum": ["completed_25", "completed_50", "completed_75"]}}}