{"routes": [{"method": "GET", "path": "/broadcast-filters", "handler": "broadcast-filter.find", "config": {"policies": []}}, {"method": "GET", "path": "/broadcast-filters/count", "handler": "broadcast-filter.count", "config": {"policies": []}}, {"method": "GET", "path": "/broadcast-filters/:id", "handler": "broadcast-filter.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/broadcast-filters", "handler": "broadcast-filter.create", "config": {"policies": []}}, {"method": "PUT", "path": "/broadcast-filters/:id", "handler": "broadcast-filter.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/broadcast-filters/:id", "handler": "broadcast-filter.delete", "config": {"policies": []}}]}