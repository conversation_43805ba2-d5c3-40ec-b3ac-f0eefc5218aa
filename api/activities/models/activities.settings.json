{"kind": "collectionType", "collectionName": "activities", "info": {"name": "Activities", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"activity_type": {"type": "enumeration", "enum": ["video", "assessment", "document"], "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "video": {"model": "videos"}, "unit": {"model": "unit"}, "final_activity": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}}, "assessment": {"model": "assessments"}, "document": {"model": "documents"}, "index": {"pluginOptions": {"i18n": {"localized": true}}, "type": "integer"}}}