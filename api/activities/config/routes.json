{"routes": [{"method": "GET", "path": "/activities", "handler": "activities.find", "config": {"policies": []}}, {"method": "GET", "path": "/activities/count", "handler": "activities.count", "config": {"policies": []}}, {"method": "GET", "path": "/activities/:id", "handler": "activities.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/activities", "handler": "activities.create", "config": {"policies": []}}, {"method": "PUT", "path": "/activities/:id", "handler": "activities.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/activities/:id", "handler": "activities.delete", "config": {"policies": []}}]}