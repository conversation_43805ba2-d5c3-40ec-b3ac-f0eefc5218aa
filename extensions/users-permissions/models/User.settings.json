{"kind": "collectionType", "collectionName": "users-permissions_user", "info": {"name": "user", "description": ""}, "options": {"draftAndPublish": false, "timestamps": true}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true}, "confirmationToken": {"type": "string", "configurable": false, "private": true}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"model": "role", "via": "users", "plugin": "users-permissions", "configurable": false}, "phone": {"type": "biginteger"}, "profile": {"type": "boolean"}, "name": {"type": "string"}, "country": {"type": "string"}, "profession": {"type": "string"}, "organization": {"type": "string"}, "gender": {"type": "string"}, "locale": {"type": "string"}, "approved": {"type": "boolean", "default": false}, "unitCount": {"type": "integer", "default": 0}, "dateCompleted": {"type": "string"}, "activityCount": {"type": "integer", "default": 0}, "assessmentBadge": {"type": "boolean"}, "videoCount": {"type": "integer", "default": 0}, "faqBadge": {"type": "boolean"}, "area": {"type": "enumeration", "enum": ["Mukono", "<PERSON><PERSON>"]}, "admin": {"type": "boolean"}, "location": {"type": "string"}, "district": {"type": "string"}, "u1_score": {"type": "integer"}, "u2_score": {"type": "integer"}, "u3_score": {"type": "integer"}, "u4_score": {"type": "integer"}, "u5_score": {"type": "integer"}, "u6_score": {"type": "integer"}}}